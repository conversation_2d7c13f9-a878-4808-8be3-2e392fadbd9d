/Users/<USER>/Documents/work/augment-projects/material-review/za-video-asr-master/app/server.py:41: DeprecationWarning: 
        on_event is deprecated, use lifespan event handlers instead.

        Read more about it in the
        [FastAPI docs for Lifespan Events](https://fastapi.tiangolo.com/advanced/events/).
        
  @app.on_event("startup")
2025-07-31 10:16:27,868 [**********] INFO [server] [server.py:84] [trace=,span=] - Started server process [10212]
2025-07-31 10:16:27,868 [**********] INFO [on] [on.py:48] [trace=,span=] - Waiting for application startup.
/Users/<USER>/Library/Python/3.9/lib/python/site-packages/urllib3/__init__.py:35: NotOpenSSLWarning: urllib3 v2 only supports OpenSSL 1.1.1+, currently the 'ssl' module is compiled with 'LibreSSL 2.8.3'. See: https://github.com/urllib3/urllib3/issues/3020
  warnings.warn(
start config service log
/Users/<USER>/Documents/work/augment-projects/material-review/za-video-asr-master/logs/leiyudeMacBook-Pro.local_app_za-video-asr_8d96a4_lt_info.log
/Users/<USER>/Documents/work/augment-projects/material-review/za-video-asr-master/logs/leiyudeMacBook-Pro.local_app_za-video-asr_8d96a4_lt_error.log
/Users/<USER>/Documents/work/augment-projects/material-review/za-video-asr-master/logs/leiyudeMacBook-Pro.local-ss_micro_app_za-video-asr_8d96a4_lt_biz.log
Notice: ffmpeg is not installed. torchaudio is used to load audio
If you want to use ffmpeg backend to load audio, please install it by:
	sudo apt install ffmpeg # ubuntu
	# brew install ffmpeg # mac
start loading depth_estimate model...
Building prefix dict from the default dictionary ...
DEBUG:jieba:Building prefix dict from the default dictionary ...
Dumping model to file cache /var/folders/4d/6rltj9j16836pnbnd7_z84z00000gn/T/jieba.cache
DEBUG:jieba:Dumping model to file cache /var/folders/4d/6rltj9j16836pnbnd7_z84z00000gn/T/jieba.cache
Loading model cost 0.252 seconds.
DEBUG:jieba:Loading model cost 0.252 seconds.
Prefix dict has been built successfully.
DEBUG:jieba:Prefix dict has been built successfully.
funasr version: 1.2.6.
Downloading Model from https://www.modelscope.cn to directory: /Users/<USER>/.cache/modelscope/hub/models/iic/speech_seaco_paraformer_large_asr_nat-zh-cn-16k-common-vocab8404-pytorch
Downloading Model from https://www.modelscope.cn to directory: /Users/<USER>/.cache/modelscope/hub/models/iic/speech_fsmn_vad_zh-cn-16k-common-pytorch
Downloading Model from https://www.modelscope.cn to directory: /Users/<USER>/.cache/modelscope/hub/models/iic/punc_ct-transformer_cn-en-common-vocab471067-large
✅ 完整FunASR模型加载成功（包含标点符号模型）
load depth_estimate model success!
all models are ready
2025-07-31 10:16:47,978 [**********] INFO [on] [on.py:62] [trace=,span=] - Application startup complete.
2025-07-31 10:16:47,978 [**********] INFO [server] [server.py:216] [trace=,span=] - Uvicorn running on http://0.0.0.0:8080 (Press CTRL+C to quit)
2025-07-31 10:16:49,810 [**********] INFO [h11_impl] [h11_impl.py:473] [trace=,span=] - 127.0.0.1:52010 - "GET /health HTTP/1.1" 200
2025-07-31 10:16:53,999 [**********] INFO [h11_impl] [h11_impl.py:473] [trace=,span=] - 127.0.0.1:52039 - "GET /health HTTP/1.1" 200
2025-07-31 10:16:54,876 [**********] INFO [h11_impl] [h11_impl.py:473] [trace=,span=] - 127.0.0.1:52051 - "GET /health HTTP/1.1" 200
