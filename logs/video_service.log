INFO:     Started server process [10596]
INFO:     Waiting for application startup.
INFO:     Application startup complete.
INFO:     Uvicorn running on http://0.0.0.0:8081 (Press CTRL+C to quit)
✅ 视频分析库加载成功
============================================================
启动真实视频内容分析服务
============================================================
🔧 正在初始化真实分析器...
  ✅ RapidOCR初始化成功
  ✅ 人脸检测器初始化成功
  🎤 检查ASR服务: http://localhost:8080/health
  ✅ ASR服务连接成功
🎉 3/3 个分析器初始化成功
🚀 服务启动模式: 真实视频内容分析
  📝 OCR: RapidOCR (真实文字识别)
  👤 人脸检测: OpenCV Haar Cascade
  🎬 视频处理: MoviePy (真实帧提取)
  📊 二维码检测: OpenCV QRCodeDetector
服务地址: http://localhost:8081
API文档: http://localhost:8081/docs
健康检查: http://localhost:8081/health
Demo页面: http://localhost:8081/demo
============================================================
INFO:     127.0.0.1:52045 - "GET /demo HTTP/1.1" 200 OK
INFO:     127.0.0.1:52045 - "GET /favicon.ico HTTP/1.1" 404 Not Found
INFO:     127.0.0.1:52049 - "GET /health HTTP/1.1" 200 OK
INFO:     127.0.0.1:52053 - "GET /health HTTP/1.1" 200 OK
🖼️  开始真实图片分析: test1.png
  📊 图片信息: 356x258
  📝 进行OCR文字识别...
    ✅ 识别到14个文字
  👤 进行人脸检测...
    ⚠️  未检测到人脸
INFO:     127.0.0.1:52089 - "POST /upload HTTP/1.1" 200 OK
